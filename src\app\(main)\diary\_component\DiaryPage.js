import DiaryCanvas from '@/app/dashboard/(tutor)/submission-management/hec-diary/review/_components/DiaryCanvas';
import Canvas from '@/components/skin/Canvas';
import SkinPreview from '@/components/skin/SkinPreview';
import Image from 'next/image';
import React, { useEffect, useState, useRef } from 'react';

const DiaryPage = ({ entry }) => {
  // Format the date for better display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (!entry) return null;

  return (
    <div className="h-full flex flex-col p-2">
      <div className="flex justify-between items- mb-2 ">
        <div className="flex justify-between items-center gap-2">
          <div
            className="px-4 py-2 text-white rounded-md"
            style={{
              background: 'linear-gradient(180deg, #ECB306 0%, #AE6E33 100%)',
            }}
          >
            <p>Stage: {entry?.settings?.level}</p>
          </div>
          <p>:</p>
          <div className="px-4 py-2 text-[#864D0D] font-medium bg-[#FFF189] rounded-md border border-dashed border-[#ECB306]">
            <p>Words: {entry?.settings?.wordLimit}</p>
          </div>
        </div>
        {entry?.score && (
          <div className="text-md text-[#864D0D] font-medium py-2 px-3 border border-dashed border-[#ECB306] bg-[#FFF189] rounded-md">
            score: {entry?.score}
          </div>
        )}
      </div>
      {/* Content area */}
      <div className=" ">
        <SkinPreview
          skin={entry.skin?.templateContent}
          contentData={{
            subject: entry.title,
            body: entry.content,
            date: entry.entryDate,
          }}
        />
      </div>
      <div className=" bg-white shadow-xl rounded-lg p-2 h-full relative">
        <div>
          <h6 className="text-[#864D0D] font-semibold text-base mb-2 text-center">
            Tutor Correction
          </h6>
        </div>
        <div className="border-b-2 py-1 flex justify-between items-center">
          <h6 className="text-lg font-medium">{entry.title}</h6>
          <p className="text-sm text-gray-500">{formatDate(entry.entryDate)}</p>
        </div>
        <div className="h-[200px] overflow-auto custom-scrollbar whitespace-pre-wrap">
          {entry?.correction?.correctionText ? (
            <p
              className="text-justify"
              dangerouslySetInnerHTML={{
                __html: entry.correction.correctionText,
              }}
            />
          ) : (
            <p className="text-center p-5">No correction yet</p>
          )}
        </div>
        {entry?.status === 'confirm' && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <h1 className="text-lg text-[#14AE5C] font-medium">Confirmed</h1>
          </div>
        )}
        <div className="absolute bottom-2 left-2">
          <button>
            <Image
              src="/assets/images/all-img/main-button.svg"
              alt="Tutor"
              width={50}
              height={50}
            />
          </button>
        </div>
      </div>

      {/* Footer with status */}
      {/* <div className="mt-4 pt-2">
        <p className="text-xs text-gray-400">
          {entry.status === 'draft' ? 'Draft' : 'Published'}
          {entry.skin && ` • ${entry.skin.name} skin`}
        </p>
      </div> */}
    </div>
  );
};

export default DiaryPage;
