'use client';
import React, { useState, useRef } from 'react';
import Button, { ButtonIcon } from '@/components/Button';
import Modal from '@/components/Modal';
import Tooltip from '@/components/Tooltip';
import SelectSkinContent from './modalContents/SelectSkinContent';
import ShareModalContent from './modalContents/ShareModalContent';
import MessageModal from './modalContents/MessageModal';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import { setIsSkinModalOpen } from '@/store/features/diarySlice';
import EmojiSelectorModal from './EmojiSelectorModal';

const DiaryIconsSidebar = ({
  todayEntry,
  className = '',
  showSkin = true,
  showShare = true,
  showDecoration = true,
  showMessage = true,
  showAddFriend = true
}) => {
  const [activeModal, setActiveModal] = useState(null);
  const [isEmojiSelectorOpen, setIsEmojiSelectorOpen] = useState(false);
  const DecorationButtonRef = useRef(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(
    todayEntry?.hasGreeting === false
  );
  console.log(todayEntry, 'hi how do u do');

  // Define all available icons with their actions
  const allIcons = [
    {
      icon: 'arcticons:image-combiner',
      alt: 'Skin',
      modalTitle: 'Change Skin',
      modalWidth: '4xl',
      action: () => dispatch(setIsSkinModalOpen(true)),
      show: showSkin,
    },
    {
      icon: 'ic:baseline-share',
      alt: 'Share',
      modalTitle: 'Share Diary',
      modalWidth: 'xl',
      show: showShare,
    },
    {
      icon: 'mdi:brush',
      alt: 'Decoration',
      modalTitle: 'Customize Diary',
      show: showDecoration,
    },
    {
      icon: 'tabler:mail',
      alt: 'Message',
      modalTitle: 'Send Message',
      action: () => setIsMessageModalOpen(true),
      show: showMessage,
    },
    {
      icon: 'mdi:account-plus',
      alt: 'Add Friend',
      modalTitle: 'Add Friend',
      show: showAddFriend,
    },
  ];

  // Filter icons based on show props
  const icons = allIcons.filter(icon => icon.show);

  const handleIconClick = (modalId, customAction) => {
    if (customAction) {
      customAction();
    } else if (modalId === 'Decoration') {
      setIsEmojiSelectorOpen(true);
    } else {
      setActiveModal(modalId);
    }
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  const handleEmojiSelect = (emoji) => {
    console.log('Selected emoji:', emoji);
    // Add your logic to handle the selected emoji here
    setIsEmojiSelectorOpen(false);
  };

  // Modal content based on the active modal
  const getModalContent = () => {
    switch (activeModal) {
      case 'Skin':
        return <SelectSkinContent />;

      case 'Location':
        return (
          <div>
            <p className="mb-4">Add a location to your diary entry.</p>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search for a location
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                placeholder="Enter a location..."
              />
            </div>
            <div className="h-64 bg-gray-100 rounded-md mb-4 flex items-center justify-center">
              <p className="text-gray-500">Map will be displayed here</p>
            </div>
          </div>
        );
      case 'Share':
        return <ShareModalContent />;

      case 'Add Friend':
        router.push('/find-friends');
      default:
        return <p>Select an action from the sidebar.</p>;
    }
  };

  return (
    <>
      <div className={` flex flex-col space-y-4 ${className}`}>
        {icons.map(({ icon, alt, action }, index) => (
          <div key={index} className="w-8 h-8 cursor-pointer">
            <Tooltip
              content={alt}
              color="user"
              size="lg"
              delay={100}
              className="-ml-3 "
              position="right"
            >
              <ButtonIcon
                icon={icon}
                innerBtnCls="h-12 w-12"
                btnIconCls="h-5 w-5"
                aria-label={alt}
                onClick={() => handleIconClick(alt, action)}
                ref={alt === 'Decoration' ? DecorationButtonRef : null}
              />
            </Tooltip>
          </div>
        ))}
      </div>

      {/* Modal for each icon action */}
      {activeModal && (
        <Modal
          isOpen={!!activeModal}
          onClose={closeModal}
          position="center"
          title={
            icons.find((icon) => icon.alt === activeModal)?.modalTitle ||
            activeModal
          }
          width={
            icons.find((icon) => icon.alt === activeModal)?.modalWidth || 'md'
          }
        >
          {getModalContent()}
          {/* <div className="mt-6 flex justify-end space-x-3">
            <Button
              buttonText="Cancel"
              onClick={closeModal}
              className="bg-gray-200 text-gray-800 hover:bg-gray-300"
            />
            <Button
              buttonText="Confirm"
              onClick={closeModal}
              className="bg-yellow-500 text-white hover:bg-yellow-600"
            />
          </div> */}
        </Modal>
      )}

      {/* Emoji Selector Modal */}
      <EmojiSelectorModal
        isOpen={isEmojiSelectorOpen}
        onClose={() => setIsEmojiSelectorOpen(false)}
        onSelect={handleEmojiSelect}
        triggerRef={DecorationButtonRef}
        position="left"
      />

      {/* Message Modal */}
      {/* <MessageModal
        isOpen={isMessageModalOpen}
        onClose={() => setIsMessageModalOpen(false)}
        // isAutoOpened={todayEntry?.hasGreeting === false}
      /> */}
    </>
  );
};

export default DiaryIconsSidebar;
