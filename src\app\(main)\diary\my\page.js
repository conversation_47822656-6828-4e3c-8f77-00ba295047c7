'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ButtonIcon } from '@/components/Button';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCover from '../_component/DiaryCover';
import DiaryContent from '../_component/DiaryContent';
import DiaryPagination from '../_component/DiaryPagination';
import DiaryIconsSidebar from '../_component/DiaryIconsSidebar';

export default function MyDiary() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const { data, isLoading, error } = useDataFetch({
    queryKey: 'diary-entries',
    endPoint: 'diary/entries',
  });

  const entries = data?.items || [];
  const hasEntries = entries.length > 0;

  // Handle loading and error states
  if (isLoading) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-200px)] items-center">
        <div className="text-center">
          <p className="text-lg">Loading diary entries...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center min-h-[calc(100vh-200px)] items-center">
        <div className="text-center text-red-500">
          <p className="text-lg">Error loading diary entries</p>
          <p className="text-sm">{error.message}</p>
        </div>
      </div>
    );
  }

  const handleOpen = () => {
    if (hasEntries) {
      setCurrentIndex(0);
      setIsOpen(true);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleLeftArrow = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 2);
    }
  };

  const handleRightArrow = () => {
    if (!isOpen && hasEntries) {
      // If diary is closed and there are entries, open it like the Open button
      handleOpen();
    } else if (currentIndex + 2 < entries.length) {
      // If diary is already open, navigate to next entries
      setCurrentIndex(currentIndex + 2);
    }
  };

  return (
    <div className="flex justify-center min-h-[calc(100vh-200px)]">
      <div
        className={`relative ${
          isOpen ? 'w-full max-w-[900px]' : 'w-full max-w-[647px]'
        } h-[658px] bg-[#FFFAC2] rounded-lg shadow-lg border border-gray-300 transition-all duration-300`}
      >
        {/* Show either the diary cover or the diary content based on isOpen state */}
        {!isOpen ? (
          <DiaryCover hasEntries={hasEntries} onOpen={handleOpen} />
        ) : (
          <>
            <DiaryContent entries={entries} currentIndex={currentIndex} />

            {/* Close button positioned outside the pages */}
            <div className="absolute top-4 right-[-50px] z-10">
              <ButtonIcon
                icon="mdi:close"
                innerBtnCls="h-10 w-10 cursor-pointer"
                btnIconCls="h-5 w-5"
                aria-label="close diary"
                onClick={handleClose}
              />
            </div>
          </>
        )}

        {/* Pagination controls */}
        <DiaryPagination
          hasEntries={hasEntries}
          isOpen={isOpen}
          currentIndex={currentIndex}
          totalEntries={entries.length}
          onLeftClick={handleLeftArrow}
          onRightClick={handleRightArrow}
        />
      </div>
    </div>
  );
}
