'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { useMutation } from '@tanstack/react-query';
import useDataFetch from '@/hooks/useDataFetch';
import SkinPreview from '@/components/skin/SkinPreview';
import { formatDate } from '@/utils/dateFormatter';
import FeedbackViewModal from '../../_component/FeedbackViewModal';
import api from '@/lib/api';

const SharedDiaryDetails = () => {
  const { id } = useParams();
  const router = useRouter();

  const {
    data: diaryEntry,
    isLoading,
    error,
  } = useDataFetch({
    queryKey: ['shared-diary-details', id],
    endPoint: `/diary/entries/${id}`,
    enabled: !!id,
  });

  const [liked, setLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  useEffect(() => {
    if (diaryEntry) {
      setLiked(diaryEntry.hasLiked);
      setLikeCount(diaryEntry.likeCount || 0);
    }
  }, [diaryEntry]);

  const likeMutation = useMutation({
    mutationFn: async ({ entryId, isLiked }) => {
      if (isLiked) {
        return await api.delete(`/diary/entries/${entryId}/like`);
      }
      return await api.post(`/diary/entries/${entryId}/like`);
    },
    onMutate: async ({ isLiked }) => {
      setLiked(!isLiked);
      setLikeCount((prev) => (isLiked ? prev - 1 : prev + 1));
      return { prevLiked: isLiked };
    },
    onError: (_err, _vars, context) => {
      setLiked(context.prevLiked);
      setLikeCount((prev) => (context.prevLiked ? prev + 1 : prev - 1));
    },
  });

  const handleLike = () => {
    likeMutation.mutate({ entryId: id, isLiked: liked });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#432005] mx-auto mb-4"></div>
          <p className="text-lg text-[#432005]">Loading diary entry...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-red-600 mb-4">Error loading diary entry</p>
          <button
            onClick={() => router.back()}
            className="bg-[#432005] text-white px-6 py-2 rounded-lg hover:bg-[#5a2a07] transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!diaryEntry) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-4">Diary entry not found</p>
          <button
            onClick={() => router.back()}
            className="bg-[#432005] text-white px-6 py-2 rounded-lg hover:bg-[#5a2a07] transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const CorrectionData = diaryEntry.correction;

  return (
    <div className="min-h-screen ">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-[#432005] hover:text-[#5a2a07] transition-colors mb-4"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to Shared Diaries
          </button>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl font-bold text-[#432005]">
                {diaryEntry.title}
              </h1>
              <span className="text-sm text-gray-500">
                 {formatDate(diaryEntry.entryDate, 'ordinal')}
              </span>
            </div>

            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                <Image
                  src={
                    diaryEntry.user?.profilePicture || '/assets/images/all-img/avatar.png'
                  }
                  alt={diaryEntry.user?.name || 'Author'}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Name</p>
                <p className="font-semibold text-[#432005]">
                  {diaryEntry.diary?.userName || 'Anonymous'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4 pt-4 border-t border-gray-200">
              <div
                className="flex items-center space-x-1 cursor-pointer"
                onClick={handleLike}
              >
                <Icon
                  icon={liked ? 'mdi:like' : 'mdi:like-outline'}
                  className={`text-2xl transition-colors duration-200 ${
                    liked ? 'text-[#CC8A02]' : 'text-[#432005]'
                  }`}
                />
                <span className="text-lg font-bold text-[#432005]">
                  {likeCount}
                </span>
                <span className="text-sm text-gray-600">likes</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-pink-100 shadow-lg overflow-hidden p-2">
          <div className="grid grid-cols-2 gap-2">
            <div className="bg-white p-2">
              <div className="flex justify-between items- mb-2 ">
                <div className="flex justify-between items-center gap-2">
                  <div
                    className="px-4 py-2 text-white rounded-md"
                    style={{
                      background:
                        'linear-gradient(180deg, #ECB306 0%, #AE6E33 100%)',
                    }}
                  >
                    <p>Stage: {diaryEntry?.settings?.level}</p>
                  </div>
                  <p>:</p>
                  <div className="px-4 py-2 text-[#864D0D] font-medium bg-[#FFF189] rounded-md border border-dashed border-[#ECB306]">
                    <p>Words: {diaryEntry?.settings?.wordLimit}</p>
                  </div>
                </div>
                {diaryEntry?.score && (
                  <div className="text-sm text-[#864D0D] font-medium py-2 px-3 border border-dashed border-[#ECB306] bg-[#FFF189] rounded-md">
                    score: {diaryEntry?.score}
                  </div>
                )}
              </div>
              <div className="mt-20">
                <SkinPreview
                  skin={diaryEntry.skin?.templateContent}
                  contentData={{
                    subject: diaryEntry.title,
                    body: diaryEntry.content,
                    date: diaryEntry.entryDate,
                  }}
                />
              </div>
            </div>
            <div className="bg-white p-2">
              <div className="mb-4 rounded-md shadow-lg p-4 ">
                <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                  <h3 className="text-lg font-semibold mb-2">
                    {diaryEntry.title}
                  </h3>
                  <div className="flex items-center gap-3 text-sm">
                    {formatDate(diaryEntry.entryDate, 'ordinal')}
                  </div>
                </div>
                <p className="whitespace-pre-wrap text-sm text-[#314158] h-[200px] custom-scrollbar overflow-y-auto">
                  {diaryEntry.content}
                </p>
              </div>
              <div className="p-3 rounded-md bg-white shadow-xl  relative mt-2">
                {CorrectionData ? (
                  <div className="">
                    <div className="">
                      <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                        Tutor Review Zone
                      </p>
                      <div className=" flex justify-between items-center border-b-2  border-dashed border-gray-300 mb-3">
                        <h3 className="text-lg font-semibold mb-2">
                          {diaryEntry?.title}
                        </h3>
                        <div className="flex items-center gap-3 text-sm">
                          {formatDate(diaryEntry.entryDate, 'ordinal')}
                        </div>
                      </div>
                      <p className="h-[200px] overflow-auto custom-scrollbar"
                        dangerouslySetInnerHTML={{
                          __html: CorrectionData.correctionText || '',
                        }}
                      />
                      {diaryEntry?.status === 'confirm' && (
                        <div className="text-center p-5">
                          <h1 className="text-lg text-[#14AE5C] font-medium">
                            Confirmed
                          </h1>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <p className="text-[#864D0D] italic">
                      No Correction available yet.
                    </p>
                  </div>
                )}

                {diaryEntry?.feedbacks?.length > 0 && (
                  <div className="absolute right-0 bottom-0">
                    <button
                      onClick={() => setShowFeedbackModal(true)}
                      aria-label="View feedback"
                    >
                      <Image
                        src="/assets/images/all-img/feedback-bg.png"
                        alt="Feedback"
                        width={50}
                        height={50}
                        className="w-full h-full object-contain"
                      />
                    </button>
                  </div>
                )}

                {showFeedbackModal && (
                  <FeedbackViewModal
                    isOpen={showFeedbackModal}
                    onClose={() => setShowFeedbackModal(false)}
                    feedbacks={diaryEntry?.feedbacks || []}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 flex justify-center space-x-4">
          <button
            onClick={handleLike}
            disabled={likeMutation.isPending}
            className={`px-6 py-3 rounded-full font-semibold border transition-all duration-200 flex items-center space-x-2 ${
              liked
                ? 'bg-gradient-to-br from-[#CC8A02] to-[#B8750A] text-white border-[#CC8A02] hover:shadow-lg'
                : 'bg-gradient-to-br from-[#FFF8E1] to-[#F5E6A3] text-[#432005] border-[#E6D16A] hover:shadow-lg'
            } ${likeMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <Icon
              icon={liked ? 'mdi:like' : 'mdi:like-outline'}
              className="text-lg"
            />
            <span>{liked ? 'Unlike' : 'Like'}</span>
            {likeMutation.isPending && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SharedDiaryDetails;
