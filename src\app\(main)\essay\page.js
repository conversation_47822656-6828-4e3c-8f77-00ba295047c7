'use client';

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import { ButtonIcon } from '@/components/Button';
import EssayFeedBackModal from './_components/FeedbackModal';
import DiaryIconsSidebar from '../diary/_component/DiaryIconsSidebar';
import Tooltip from '@/components/Tooltip';
import { selectIsSkinModalOpen, setIsSkinModalOpen } from '@/store/features/diarySlice';
import SelectSkinContent from '../diary/_component/modalContents/SelectSkinContent';

export default function WriteEssay() {
  const router = useRouter();
  const dispatch = useDispatch();
  const taskId = useSearchParams().get('taskId');
  const [isSaving, setIsSaving] = useState(false);
  const [modalData, setModalData] = useState(null);
  const [showError, setShowError] = useState(false);
  const {isSkinModalOpen} = useSelector((state) => state.diary.isSkinModalOpen);

  if (!taskId) return router.push('/essay/mission');

  const icons = [
    {
      icon: 'arcticons:image-combiner',
      alt: 'Skin',
      modalTitle: 'Change Skin',
      modalWidth: '4xl',
      action: () => dispatch(setIsSkinModalOpen(true)),
    },
  ];

  const { data: submissionDetails, refetch } = useDataFetch({
    queryKey: ['/student-essay/submissions'],
    endPoint: `/student-essay/submissions/${taskId}`,
  });

  const latestSubmission = submissionDetails?.submissionHistory?.at(-1) || {};

  const [subject, setSubject] = useState(latestSubmission?.title || ' ');
  const [body, setBody] = useState(latestSubmission?.content || '');
  const [wordCount, setWordCount] = useState(0); // Single declaration
  const [date, setDate] = useState(() =>
    latestSubmission?.submissionDate
      ? new Date(latestSubmission?.submissionDate).toISOString().slice(0, 10)
      : new Date().toISOString().slice(0, 10)
  );

  const { data: skinInfo, isLoading } = useDataFetch({
    queryKey: ['essay-skin-info', taskId],
    endPoint: `/student-essay/skins/${taskId}`,
    enabled: !!taskId,
  });

  const countWords = (html) => {
    if (!html) return 0;
    const text = html.replace(/<[^>]*>/g, ' ');
    const cleanText = text.replace(/&nbsp;|&|<|>|"|'/g, ' ');
    return cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  useEffect(() => {
    setWordCount(countWords(body));
  }, [body]);

  const handleSave = async () => {
    if (
      wordCount < submissionDetails?.task?.wordLimitMinimum ||
      wordCount > submissionDetails?.task?.wordLimitMaximum
    ) {
      setShowError(true);
      return;
    }
    setIsSaving(true);

    const payload = {
      taskId: taskId,
      // skinId: essayId,
      title: subject,
      content: body,
    };

    try {
      const response = await api.post('/student-essay/submit/task', payload);
      refetch();
      setShowError(false);
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };



  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 relative">
      <div className="p-2 bg-[#FDE7E9] flex flex-col lg:flex-row items-center gap-3">
        <div className="flex-1 bg-white rounded-lg h-full space-y-5 text-center p-5 w-full">
          <SkinPreview
            skin={skinInfo?.moduleDefaultSkin.skin.templateContent}
            contentData={{
              subject,
              body,
              date,
            }}
          />

          <button
            onClick={handleSave}
            disabled={isSaving}
            className={`  text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
            border-2 border-yellow-100
            shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
            transition-all duration-300
            bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
            relative pr-8
            ring-2 ring-[#A36105] ${
              isSaving
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-yellow-400 hover:bg-yellow-300'
            }`}
          >
            {isSaving ? 'Saving...' : 'Submit'}
          </button>
        </div>

        {/* Example inputs for subject, body, and date */}
        <div className="flex-1 bg-white rounded-lg p-3 space-y-3 w-full">
          <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
            <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Essay Title"
                className="p-2 mr-2 w-full focus:outline-[1px] focus:outline-gray-200"
              />
              <input
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="border p-2 mr-2 rounded-lg"
              />
            </div>
            <textarea
              value={body}
              onChange={(e) => setBody(e.target.value)}
              placeholder="Essay"
              className={`border min-h-60 ${
                showError && 'border-red-500'
              } p-2 w-full rounded focus:outline-[1px] focus:outline-gray-400 shadow-[inset_2px_2px_6px_0px_#0000001F]`}
              rows={4}
            />

            <div className="text-sm flex items-center justify-between">
              <span>{`${wordCount} / ${
                submissionDetails?.task?.wordLimitMaximum || Infinity
              } (word)`}</span>

              {showError &&
                wordCount < submissionDetails?.task?.wordLimitMinimum && (
                  <span className="text-red-500">
                    Minimum {submissionDetails?.task?.wordLimitMinimum} words
                    required.
                  </span>
                )}
            </div>
          </div>

          <div className="h-full min-h-64 shadow-lg border rounded-lg p-2 relative">
            <div className="h-full">
              <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                Tutor Review Zone
              </p>
              <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                <h3 className="mb-2">{subject}</h3>
                <div className="flex items-center gap-3 text-sm">{date}</div>
              </div>
              <div
                className="min-h-28"
                dangerouslySetInnerHTML={{
                  __html: body || '',
                }}
              />
              {/* {todayEntry?.status === 'confirm' && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                  <h1 className="text-lg text-[#14AE5C] font-medium">
                    Confirmed
                  </h1>
                </div>
              )} */}
            </div>

            <span className="absolute right-2 bottom-2">
              <ButtonIcon
                icon="tabler:message-2-star"
                innerBtnCls={`h-12 w-12`}
                btnIconCls={`h-5 w-5`}
                onClick={() => setModalData(body)}
                aria-label=""
                withbackground={false}
              />
            </span>
          </div>
        </div>
      </div>

      <EssayFeedBackModal
        isOpen={!!modalData}
        onClose={() => setModalData(null)}
        data={''}
      />

      <div className={`absolute top-5 -right-10 flex flex-col space-y-4`}>
        {icons.map(({ icon, alt, action }, index) => (
          <div key={index} className="w-8 h-8 cursor-pointer">
            <Tooltip
              content={alt}
              color="user"
              size="lg"
              delay={100}
              className="-ml-3 "
              position="right"
            >
              <ButtonIcon
                icon={icon}
                innerBtnCls="h-12 w-12"
                btnIconCls="h-5 w-5"
                aria-label={alt}
                onClick={() => action}
                ref={alt === 'Decoration' ? DecorationButtonRef : null}
              />
            </Tooltip>
          </div>
        ))}
      </div>

      { isSkinModalOpen && <SelectSkinContent />}
    </div>
  );
}
